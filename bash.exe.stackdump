Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEF4B50000 ntdll.dll
7FFEF3D80000 KERNEL32.DLL
7FFEF2400000 KERNELBASE.dll
7FFEF2880000 USER32.dll
7FFEF1E50000 win32u.dll
000210040000 msys-2.0.dll
7FFEF4A20000 GDI32.dll
7FFEF1E80000 gdi32full.dll
7FFEF1DB0000 msvcp_win.dll
7FFEF21E0000 ucrtbase.dll
7FFEF4A50000 advapi32.dll
7FFEF37C0000 msvcrt.dll
7FFEF3870000 sechost.dll
7FFEF1C40000 bcrypt.dll
7FFEF4900000 RPCRT4.dll
7FFEF12F0000 CRYPTBASE.DLL
7FFEF2380000 bcryptPrimitives.dll
7FFEF3D40000 IMM32.DLL
