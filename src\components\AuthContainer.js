
import React, { useState } from 'react';
import { Box, Container, Paper } from '@mui/material';
import { useAuth } from '../contexts/AuthContext.js';
import LoginForm from './LoginForm.js';
import SignUpForm from './SignUpForm.js';
import ForgotPasswordForm from './ForgotPasswordForm.js';
import UserDashboard from './UserDashboard.js';

const AuthContainer = () => {
  const [currentView, setCurrentView] = useState('login');
  const { user } = useAuth();

  if (user) {
    return React.createElement(UserDashboard);
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'login':
        return React.createElement(LoginForm, {
          onSwitchToSignUp: () => setCurrentView('signup'),
          onSwitchToForgotPassword: () => setCurrentView('forgot-password')
        });
      case 'signup':
        return React.createElement(SignUpForm, {
          onSwitchToLogin: () => setCurrentView('login')
        });
      case 'forgot-password':
        return React.createElement(ForgotPasswordForm, {
          onSwitchToLogin: () => setCurrentView('login')
        });
      default:
        return null;
    }
  };

  return React.createElement(
    Box,
    {
      sx: {
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: { xs: 2, sm: 3 },
        px: { xs: 1, sm: 2 },
      }
    },
    React.createElement(
      Container,
      { maxWidth: "sm" },
      React.createElement(
        Paper,
        {
          elevation: 24,
          sx: {
            p: { xs: 3, sm: 4 },
            borderRadius: 2,
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            width: '100%',
            maxWidth: 480,
            mx: 'auto',
          }
        },
        renderCurrentView()
      )
    )
  );
};

export default AuthContainer;
