
import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Link,
  InputAdornment,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Mail } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext.js';
import { toast } from 'sonner';

const ForgotPasswordForm = ({ onSwitchToLogin }) => {
  const [email, setEmail] = useState('');
  const [errors, setErrors] = useState({});
  const [emailSent, setEmailSent] = useState(false);
  
  const { forgotPassword, isLoading } = useAuth();

  const validateForm = () => {
    const newErrors = {};

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (value) => {
    setEmail(value);
    if (errors.email) {
      setErrors(prev => ({ ...prev, email: '' }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      await forgotPassword(email);
      setEmailSent(true);
      toast.success('Password reset email sent!');
    } catch (error) {
      toast.error('Failed to send reset email. Please try again.');
    }
  };

  if (emailSent) {
    return React.createElement(
      Box,
      { sx: { width: '100%' } },
      React.createElement(
        Typography,
        {
          variant: "h5",
          component: "h1",
          gutterBottom: true,
          sx: {
            fontWeight: 700,
            color: '#1a1a1a',
            mb: 1,
            textAlign: 'center',
            fontSize: { xs: '1.3rem', sm: '1.5rem' }
          }
        },
        "Check Your Email"
      ),
      React.createElement(
        Typography,
        {
          variant: "body2",
          sx: {
            color: '#666',
            mb: 3,
            textAlign: 'center',
            fontSize: '0.875rem'
          }
        },
        `We've sent a password reset link to ${email}`
      ),
      React.createElement(
        Alert,
        { severity: "success", sx: { mb: 2.5, borderRadius: 1.5, fontSize: '0.875rem' } },
        React.createElement(
          Typography,
          { variant: "body2", sx: { fontSize: '0.85rem' } },
          React.createElement("strong", null, "Email sent successfully!"),
          React.createElement("br"),
          "Please check your inbox and click the reset link to create a new password. Don't forget to check your spam folder if you don't see the email."
        )
      ),
      React.createElement(
        Box,
        { sx: { mb: 2.5 } },
        React.createElement(
          Typography,
          { variant: "body2", sx: { color: '#666', mb: 1.5, fontSize: '0.85rem' } },
          "Didn't receive the email?"
        ),
        React.createElement(
          Button,
          {
            variant: "outlined",
            onClick: () => setEmailSent(false),
            size: "small",
            sx: {
              borderRadius: 1.5,
              textTransform: 'none',
              fontWeight: 600,
              px: 2,
              py: 0.75,
              fontSize: '0.875rem',
            }
          },
          "Try again"
        )
      ),
      React.createElement(
        Typography,
        { variant: "body2", sx: { textAlign: 'center', color: '#666', fontSize: '0.85rem' } },
        "Remember your password? ",
        React.createElement(
          Link,
          {
            component: "button",
            type: "button",
            onClick: onSwitchToLogin,
            sx: {
              color: '#1976d2',
              textDecoration: 'none',
              fontWeight: 600,
              '&:hover': {
                textDecoration: 'underline',
              },
            }
          },
          "Back to sign in"
        )
      )
    );
  }

  return React.createElement(
    Box,
    { component: "form", onSubmit: handleSubmit, sx: { width: '100%' } },
    React.createElement(
      Typography,
      {
        variant: "h5",
        component: "h1",
        gutterBottom: true,
        sx: {
          fontWeight: 700,
          color: '#1a1a1a',
          mb: 1,
          textAlign: 'center',
          fontSize: { xs: '1.3rem', sm: '1.5rem' }
        }
      },
      "Forgot Password"
    ),
    React.createElement(
      Typography,
      {
        variant: "body2",
        sx: {
          color: '#666',
          mb: 3,
          textAlign: 'center',
          fontSize: '0.875rem'
        }
      },
      "Enter your email address and we'll send you a link to reset your password"
    ),
    React.createElement(TextField, {
      fullWidth: true,
      label: "Email Address",
      type: "email",
      value: email,
      onChange: (e) => handleInputChange(e.target.value),
      error: !!errors.email,
      helperText: errors.email,
      disabled: isLoading,
      size: "small",
      InputProps: {
        startAdornment: React.createElement(
          InputAdornment,
          { position: "start" },
          React.createElement(Mail, { size: 18, color: "#666" })
        ),
      },
      sx: {
        mb: 2.5,
        '& .MuiOutlinedInput-root': {
          borderRadius: 1.5,
        },
      }
    }),
    React.createElement(
      Button,
      {
        fullWidth: true,
        type: "submit",
        variant: "contained",
        disabled: isLoading,
        size: "medium",
        sx: {
          py: 1.25,
          fontSize: '0.9rem',
          fontWeight: 600,
          textTransform: 'none',
          borderRadius: 1.5,
          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
          boxShadow: '0 3px 10px rgba(25, 118, 210, 0.3)',
          '&:hover': {
            background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
            boxShadow: '0 4px 12px rgba(25, 118, 210, 0.4)',
          },
          '&:disabled': {
            background: '#ccc',
            boxShadow: 'none',
          },
        }
      },
      isLoading ? React.createElement(CircularProgress, { size: 20, color: "inherit" }) : 'Send Reset Link'
    ),
    React.createElement(
      Typography,
      { variant: "body2", sx: { mt: 2.5, textAlign: 'center', color: '#666', fontSize: '0.85rem' } },
      "Remember your password? ",
      React.createElement(
        Link,
        {
          component: "button",
          type: "button",
          onClick: onSwitchToLogin,
          sx: {
            color: '#1976d2',
            textDecoration: 'none',
            fontWeight: 600,
            '&:hover': {
              textDecoration: 'underline',
            },
          }
        },
        "Back to sign in"
      )
    )
  );
};

export default ForgotPasswordForm;
