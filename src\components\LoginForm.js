
import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Link,
  InputAdornment,
  IconButton,
  CircularProgress,
} from '@mui/material';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'sonner';
import GoogleLoginButton from './GoogleLoginButton.js';

const LoginForm = ({ onSwitchToSignUp, onSwitchToForgotPassword }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  
  const { login, isLoading } = useAuth();

  const validateForm = () => {
    const newErrors = {};

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      await login(formData.email, formData.password);
      toast.success('Successfully logged in!');
    } catch (error) {
      toast.error('Invalid email or password. Please try again.');
    }
  };

  return React.createElement(
    Box,
    { component: "form", onSubmit: handleSubmit, sx: { width: '100%' } },
    React.createElement(
      Typography,
      {
        variant: "h5",
        component: "h1",
        gutterBottom: true,
        sx: {
          fontWeight: 700,
          color: '#1a1a1a',
          mb: 1,
          textAlign: 'center',
          fontSize: { xs: '1.3rem', sm: '1.5rem' }
        }
      },
      "Welcome Back"
    ),
    React.createElement(
      Typography,
      {
        variant: "body2",
        sx: {
          color: '#666',
          mb: 3,
          textAlign: 'center',
          fontSize: '0.875rem'
        }
      },
      "Sign in to your account to continue"
    ),
    React.createElement(GoogleLoginButton),
    React.createElement(
      Box,
      {
        sx: {
          display: 'flex',
          alignItems: 'center',
          my: 2.5,
          '&::before, &::after': {
            content: '""',
            flex: 1,
            height: '1px',
            backgroundColor: '#e0e0e0',
          },
        }
      },
      React.createElement(
        Typography,
        { sx: { px: 2, color: '#666', fontSize: '0.8rem' } },
        "or continue with email"
      )
    ),

    React.createElement(TextField, {
      fullWidth: true,
      label: "Email Address",
      type: "email",
      value: formData.email,
      onChange: (e) => handleInputChange('email', e.target.value),
      error: !!errors.email,
      helperText: errors.email,
      disabled: isLoading,
      size: "small",
      InputProps: {
        startAdornment: React.createElement(
          InputAdornment,
          { position: "start" },
          React.createElement(Mail, { size: 18, color: "#666" })
        ),
      },
      sx: {
        mb: 1.5,
        '& .MuiOutlinedInput-root': {
          borderRadius: 1.5,
        },
      }
    }),

    React.createElement(TextField, {
      fullWidth: true,
      label: "Password",
      type: showPassword ? 'text' : 'password',
      value: formData.password,
      onChange: (e) => handleInputChange('password', e.target.value),
      error: !!errors.password,
      helperText: errors.password,
      disabled: isLoading,
      size: "small",
      InputProps: {
        startAdornment: React.createElement(
          InputAdornment,
          { position: "start" },
          React.createElement(Lock, { size: 18, color: "#666" })
        ),
        endAdornment: React.createElement(
          InputAdornment,
          { position: "end" },
          React.createElement(
            IconButton,
            {
              onClick: () => setShowPassword(!showPassword),
              edge: "end",
              size: "small"
            },
            showPassword ? React.createElement(EyeOff, { size: 18 }) : React.createElement(Eye, { size: 18 })
          )
        ),
      },
      sx: {
        mb: 0.5,
        '& .MuiOutlinedInput-root': {
          borderRadius: 1.5,
        },
      }
    }),

    React.createElement(
      Box,
      { sx: { textAlign: 'right', mb: 2.5 } },
      React.createElement(
        Link,
        {
          component: "button",
          type: "button",
          variant: "body2",
          onClick: onSwitchToForgotPassword,
          sx: {
            color: '#1976d2',
            textDecoration: 'none',
            fontWeight: 500,
            fontSize: '0.8rem',
            '&:hover': {
              textDecoration: 'underline',
            },
          }
        },
        "Forgot password?"
      )
    ),
    React.createElement(
      Button,
      {
        fullWidth: true,
        type: "submit",
        variant: "contained",
        disabled: isLoading,
        size: "medium",
        sx: {
          py: 1.25,
          fontSize: '0.9rem',
          fontWeight: 600,
          textTransform: 'none',
          borderRadius: 1.5,
          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
          boxShadow: '0 3px 10px rgba(25, 118, 210, 0.3)',
          '&:hover': {
            background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
            boxShadow: '0 4px 12px rgba(25, 118, 210, 0.4)',
          },
          '&:disabled': {
            background: '#ccc',
            boxShadow: 'none',
          },
        }
      },
      isLoading ? React.createElement(CircularProgress, { size: 20, color: "inherit" }) : 'Sign In'
    ),

    React.createElement(
      Typography,
      { variant: "body2", sx: { mt: 2.5, textAlign: 'center', color: '#666', fontSize: '0.85rem' } },
      "Don't have an account? ",
      React.createElement(
        Link,
        {
          component: "button",
          type: "button",
          onClick: onSwitchToSignUp,
          sx: {
            color: '#1976d2',
            textDecoration: 'none',
            fontWeight: 600,
            '&:hover': {
              textDecoration: 'underline',
            },
          }
        },
        "Sign up"
      )
    )
  );
};

export default LoginForm;
