
import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Link,
  InputAdornment,
  IconButton,
  CircularProgress,
} from '@mui/material';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'sonner';
import GoogleLoginButton from './GoogleLoginButton';

const LoginForm = ({ onSwitchToSignUp, onSwitchToForgotPassword }) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  
  const { login, isLoading } = useAuth();

  const validateForm = () => {
    const newErrors = {};

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      await login(formData.email, formData.password);
      toast.success('Successfully logged in!');
    } catch (error) {
      toast.error('Invalid email or password. Please try again.');
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
      <Typography variant="h5" component="h1" gutterBottom sx={{ 
        fontWeight: 700, 
        color: '#1a1a1a',
        mb: 1,
        textAlign: 'center',
        fontSize: { xs: '1.3rem', sm: '1.5rem' }
      }}>
        Welcome Back
      </Typography>
      
      <Typography variant="body2" sx={{ 
        color: '#666', 
        mb: 3, 
        textAlign: 'center',
        fontSize: '0.875rem'
      }}>
        Sign in to your account to continue
      </Typography>

      <GoogleLoginButton />
      
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        my: 2.5,
        '&::before, &::after': {
          content: '""',
          flex: 1,
          height: '1px',
          backgroundColor: '#e0e0e0',
        },
      }}>
        <Typography sx={{ px: 2, color: '#666', fontSize: '0.8rem' }}>
          or continue with email
        </Typography>
      </Box>

      <TextField
        fullWidth
        label="Email Address"
        type="email"
        value={formData.email}
        onChange={(e) => handleInputChange('email', e.target.value)}
        error={!!errors.email}
        helperText={errors.email}
        disabled={isLoading}
        size="small"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Mail size={18} color="#666" />
            </InputAdornment>
          ),
        }}
        sx={{ 
          mb: 1.5,
          '& .MuiOutlinedInput-root': {
            borderRadius: 1.5,
          },
        }}
      />

      <TextField
        fullWidth
        label="Password"
        type={showPassword ? 'text' : 'password'}
        value={formData.password}
        onChange={(e) => handleInputChange('password', e.target.value)}
        error={!!errors.password}
        helperText={errors.password}
        disabled={isLoading}
        size="small"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Lock size={18} color="#666" />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <IconButton
                onClick={() => setShowPassword(!showPassword)}
                edge="end"
                size="small"
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </IconButton>
            </InputAdornment>
          ),
        }}
        sx={{ 
          mb: 0.5,
          '& .MuiOutlinedInput-root': {
            borderRadius: 1.5,
          },
        }}
      />

      <Box sx={{ textAlign: 'right', mb: 2.5 }}>
        <Link
          component="button"
          type="button"
          variant="body2"
          onClick={onSwitchToForgotPassword}
          sx={{ 
            color: '#1976d2',
            textDecoration: 'none',
            fontWeight: 500,
            fontSize: '0.8rem',
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          Forgot password?
        </Link>
      </Box>

      <Button
        fullWidth
        type="submit"
        variant="contained"
        disabled={isLoading}
        size="medium"
        sx={{
          py: 1.25,
          fontSize: '0.9rem',
          fontWeight: 600,
          textTransform: 'none',
          borderRadius: 1.5,
          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
          boxShadow: '0 3px 10px rgba(25, 118, 210, 0.3)',
          '&:hover': {
            background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
            boxShadow: '0 4px 12px rgba(25, 118, 210, 0.4)',
          },
          '&:disabled': {
            background: '#ccc',
            boxShadow: 'none',
          },
        }}
      >
        {isLoading ? (
          <CircularProgress size={20} color="inherit" />
        ) : (
          'Sign In'
        )}
      </Button>

      <Typography variant="body2" sx={{ mt: 2.5, textAlign: 'center', color: '#666', fontSize: '0.85rem' }}>
        Don't have an account?{' '}
        <Link
          component="button"
          type="button"
          onClick={onSwitchToSignUp}
          sx={{ 
            color: '#1976d2',
            textDecoration: 'none',
            fontWeight: 600,
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          Sign up
        </Link>
      </Typography>
    </Box>
  );
};

export default LoginForm;
