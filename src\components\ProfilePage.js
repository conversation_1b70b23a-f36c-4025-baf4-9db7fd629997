import React, { useState } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Avatar,
  Card,
  CardContent,
  Divider,
  Chip,
  Switch,
  FormControlLabel,
  Alert,
  Grid,
} from '@mui/material';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Shield, 
  Camera,
  Save,
  Edit
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext.js';

const ProfilePage = () => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: '+****************',
    location: 'San Francisco, CA',
    bio: 'Software developer passionate about creating user-friendly applications.',
    company: 'Tech Solutions Inc.',
    role: 'Senior Developer',
    joinDate: 'January 2022',
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: true,
    emailNotifications: true,
    smsNotifications: false,
    loginAlerts: true,
  });

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSecurityChange = (setting, value) => {
    setSecuritySettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  const handleSave = () => {
    setIsEditing(false);
    // Here you would typically save to backend
    console.log('Profile saved:', profileData);
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset to original values
    setProfileData({
      name: user?.name || '',
      email: user?.email || '',
      phone: '+****************',
      location: 'San Francisco, CA',
      bio: 'Software developer passionate about creating user-friendly applications.',
      company: 'Tech Solutions Inc.',
      role: 'Senior Developer',
      joinDate: 'January 2022',
    });
  };

  return React.createElement(
    Container,
    { maxWidth: "lg", sx: { py: 4 } },
    React.createElement(
      Typography,
      { variant: "h4", sx: { mb: 4, fontWeight: 700 } },
      "Profile Settings"
    ),
    React.createElement(
      Grid,
      { container: true, spacing: 4 },
      React.createElement(
        Grid,
        { item: true, xs: 12, md: 4 },
        React.createElement(
          Paper,
          { sx: { p: 3, textAlign: 'center' } },
          React.createElement(
            Box,
            { sx: { position: 'relative', display: 'inline-block', mb: 2 } },
            React.createElement(
              Avatar,
              {
                sx: { width: 120, height: 120, bgcolor: '#1976d2', fontSize: '2rem' },
                src: user?.avatar
              },
              user?.name?.[0] || user?.email?.[0] || 'U'
            ),
            React.createElement(
              Button,
              {
                size: "small",
                sx: {
                  position: 'absolute',
                  bottom: 0,
                  right: 0,
                  minWidth: 'auto',
                  width: 36,
                  height: 36,
                  borderRadius: '50%',
                  bgcolor: '#fff',
                  boxShadow: 2,
                  '&:hover': { bgcolor: '#f5f5f5' }
                }
              },
              React.createElement(Camera, { size: 16 })
            )
          ),
          React.createElement(
            Typography,
            { variant: "h6", sx: { mb: 1 } },
            profileData.name || 'User Name'
          ),
          React.createElement(
            Typography,
            { variant: "body2", color: "textSecondary", sx: { mb: 2 } },
            profileData.role
          ),
          React.createElement(
            Chip,
            {
              label: "Verified Account",
              color: "success",
              size: "small",
              icon: React.createElement(Shield, { size: 16 })
            }
          )
        )
      ),
      React.createElement(
        Grid,
        { item: true, xs: 12, md: 8 },
        React.createElement(
          Paper,
          { sx: { p: 3, mb: 3 } },
          React.createElement(
            Box,
            { sx: { display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 } },
            React.createElement(
              Typography,
              { variant: "h6" },
              "Personal Information"
            ),
            React.createElement(
              Button,
              {
                startIcon: React.createElement(isEditing ? Save : Edit, { size: 16 }),
                onClick: isEditing ? handleSave : () => setIsEditing(true),
                variant: isEditing ? "contained" : "outlined"
              },
              isEditing ? "Save Changes" : "Edit Profile"
            )
          ),
          React.createElement(
            Grid,
            { container: true, spacing: 3 },
            React.createElement(
              Grid,
              { item: true, xs: 12, sm: 6 },
              React.createElement(TextField, {
                fullWidth: true,
                label: "Full Name",
                value: profileData.name,
                onChange: (e) => handleInputChange('name', e.target.value),
                disabled: !isEditing,
                InputProps: {
                  startAdornment: React.createElement(User, { size: 20, style: { marginRight: 8, color: '#666' } })
                }
              })
            ),
            React.createElement(
              Grid,
              { item: true, xs: 12, sm: 6 },
              React.createElement(TextField, {
                fullWidth: true,
                label: "Email",
                value: profileData.email,
                onChange: (e) => handleInputChange('email', e.target.value),
                disabled: !isEditing,
                InputProps: {
                  startAdornment: React.createElement(Mail, { size: 20, style: { marginRight: 8, color: '#666' } })
                }
              })
            ),
            React.createElement(
              Grid,
              { item: true, xs: 12, sm: 6 },
              React.createElement(TextField, {
                fullWidth: true,
                label: "Phone",
                value: profileData.phone,
                onChange: (e) => handleInputChange('phone', e.target.value),
                disabled: !isEditing,
                InputProps: {
                  startAdornment: React.createElement(Phone, { size: 20, style: { marginRight: 8, color: '#666' } })
                }
              })
            ),
            React.createElement(
              Grid,
              { item: true, xs: 12, sm: 6 },
              React.createElement(TextField, {
                fullWidth: true,
                label: "Location",
                value: profileData.location,
                onChange: (e) => handleInputChange('location', e.target.value),
                disabled: !isEditing,
                InputProps: {
                  startAdornment: React.createElement(MapPin, { size: 20, style: { marginRight: 8, color: '#666' } })
                }
              })
            ),
            React.createElement(
              Grid,
              { item: true, xs: 12 },
              React.createElement(TextField, {
                fullWidth: true,
                label: "Bio",
                value: profileData.bio,
                onChange: (e) => handleInputChange('bio', e.target.value),
                disabled: !isEditing,
                multiline: true,
                rows: 3
              })
            )
          ),
          isEditing && React.createElement(
            Box,
            { sx: { mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' } },
            React.createElement(
              Button,
              { onClick: handleCancel, variant: "outlined" },
              "Cancel"
            ),
            React.createElement(
              Button,
              { onClick: handleSave, variant: "contained" },
              "Save Changes"
            )
          )
        ),
        React.createElement(
          Paper,
          { sx: { p: 3 } },
          React.createElement(
            Typography,
            { variant: "h6", sx: { mb: 3 } },
            "Security Settings"
          ),
          React.createElement(
            Box,
            { sx: { display: 'flex', flexDirection: 'column', gap: 2 } },
            React.createElement(
              FormControlLabel,
              {
                control: React.createElement(Switch, {
                  checked: securitySettings.twoFactorAuth,
                  onChange: (e) => handleSecurityChange('twoFactorAuth', e.target.checked)
                }),
                label: React.createElement(
                  Typography,
                  { variant: "body2" },
                  "Two-Factor Authentication"
                )
              }
            ),
            React.createElement(
              FormControlLabel,
              {
                control: React.createElement(Switch, {
                  checked: securitySettings.emailNotifications,
                  onChange: (e) => handleSecurityChange('emailNotifications', e.target.checked)
                }),
                label: React.createElement(
                  Typography,
                  { variant: "body2" },
                  "Email Notifications"
                )
              }
            ),
            React.createElement(
              FormControlLabel,
              {
                control: React.createElement(Switch, {
                  checked: securitySettings.smsNotifications,
                  onChange: (e) => handleSecurityChange('smsNotifications', e.target.checked)
                }),
                label: React.createElement(
                  Typography,
                  { variant: "body2" },
                  "SMS Notifications"
                )
              }
            ),
            React.createElement(
              FormControlLabel,
              {
                control: React.createElement(Switch, {
                  checked: securitySettings.loginAlerts,
                  onChange: (e) => handleSecurityChange('loginAlerts', e.target.checked)
                }),
                label: React.createElement(
                  Typography,
                  { variant: "body2" },
                  "Login Alerts"
                )
              }
            )
          )
        )
      )
    ),
    !isEditing && React.createElement(
      Alert,
      { severity: "success", sx: { mt: 2 } },
      "Profile updated successfully!"
    )
  );
};

export default ProfilePage;
