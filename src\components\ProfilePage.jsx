
import React, { useState } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Avatar,
  Card,
  CardContent,
  Divider,
  Chip,
  Switch,
  FormControlLabel,
  Alert,
  Grid,
} from '@mui/material';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Shield, 
  Camera,
  Save,
  Edit
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const ProfilePage = () => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: '+****************',
    location: 'San Francisco, CA',
    bio: 'Software developer passionate about creating user-friendly applications.',
    company: 'Tech Solutions Inc.',
    role: 'Senior Developer',
    joinDate: 'January 2022',
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: true,
    emailNotifications: true,
    smsNotifications: false,
    loginAlerts: true,
  });

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({ ...prev, [field]: value }));
  };

  const handleSecurityChange = (setting, value) => {
    setSecuritySettings(prev => ({ ...prev, [setting]: value }));
  };

  const handleSaveProfile = () => {
    setIsEditing(false);
    // Here you would typically save to your backend
    console.log('Profile saved:', profileData);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Profile Header */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mb: 2 }}>
          <Box sx={{ position: 'relative' }}>
            <Avatar
              src={user?.profilePicture}
              sx={{ width: 100, height: 100 }}
            >
              <User size={40} />
            </Avatar>
            <Button
              size="small"
              sx={{
                position: 'absolute',
                bottom: -8,
                right: -8,
                minWidth: 32,
                width: 32,
                height: 32,
                borderRadius: '50%',
                bgcolor: 'primary.main',
                color: 'white',
                '&:hover': { bgcolor: 'primary.dark' },
              }}
            >
              <Camera size={16} />
            </Button>
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
              {profileData.name}
            </Typography>
            <Typography variant="body1" color="textSecondary" sx={{ mb: 1 }}>
              {profileData.role} at {profileData.company}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Chip 
                icon={<Mail size={14} />} 
                label={profileData.email} 
                variant="outlined" 
                size="small" 
              />
              <Chip 
                icon={<MapPin size={14} />} 
                label={profileData.location} 
                variant="outlined" 
                size="small" 
              />
            </Box>
          </Box>
          <Button
            variant="contained"
            startIcon={isEditing ? <Save size={16} /> : <Edit size={16} />}
            onClick={isEditing ? handleSaveProfile : () => setIsEditing(true)}
            sx={{ height: 'fit-content' }}
          >
            {isEditing ? 'Save Changes' : 'Edit Profile'}
          </Button>
        </Box>
      </Paper>

      <Box sx={{ flexGrow: 1 }}>
        <Grid container spacing={3}>
          {/* Personal Information */}
          <Grid xs={12} md={8}>
            <Paper sx={{ p: 3, borderRadius: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                Personal Information
              </Typography>
              <Box sx={{ flexGrow: 1 }}>
                <Grid container spacing={2}>
                  <Grid xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      value={profileData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      disabled={!isEditing}
                      size="small"
                    />
                  </Grid>
                  <Grid xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Email"
                      value={profileData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      disabled={!isEditing}
                      size="small"
                    />
                  </Grid>
                  <Grid xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Phone"
                      value={profileData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      disabled={!isEditing}
                      size="small"
                    />
                  </Grid>
                  <Grid xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Location"
                      value={profileData.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      disabled={!isEditing}
                      size="small"
                    />
                  </Grid>
                  <Grid xs={12}>
                    <TextField
                      fullWidth
                      label="Bio"
                      multiline
                      rows={3}
                      value={profileData.bio}
                      onChange={(e) => handleInputChange('bio', e.target.value)}
                      disabled={!isEditing}
                      size="small"
                    />
                  </Grid>
                  <Grid xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Company"
                      value={profileData.company}
                      onChange={(e) => handleInputChange('company', e.target.value)}
                      disabled={!isEditing}
                      size="small"
                    />
                  </Grid>
                  <Grid xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Role"
                      value={profileData.role}
                      onChange={(e) => handleInputChange('role', e.target.value)}
                      disabled={!isEditing}
                      size="small"
                    />
                  </Grid>
                </Grid>
              </Box>
            </Paper>
          </Grid>

          {/* Account Summary */}
          <Grid xs={12} md={4}>
            <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                Account Summary
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Calendar size={20} color="#666" />
                  <Box>
                    <Typography variant="body2" color="textSecondary">
                      Member Since
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {profileData.joinDate}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Shield size={20} color="#4caf50" />
                  <Box>
                    <Typography variant="body2" color="textSecondary">
                      Account Status
                    </Typography>
                    <Chip 
                      label="Verified" 
                      color="success" 
                      size="small" 
                      variant="outlined" 
                    />
                  </Box>
                </Box>
              </Box>
            </Paper>

            {/* Security Settings */}
            <Paper sx={{ p: 3, borderRadius: 2 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                Security & Privacy
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={securitySettings.twoFactorAuth}
                      onChange={(e) => handleSecurityChange('twoFactorAuth', e.target.checked)}
                      size="small"
                    />
                  }
                  label={
                    <Typography variant="body2">Two-Factor Authentication</Typography>
                  }
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={securitySettings.emailNotifications}
                      onChange={(e) => handleSecurityChange('emailNotifications', e.target.checked)}
                      size="small"
                    />
                  }
                  label={
                    <Typography variant="body2">Email Notifications</Typography>
                  }
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={securitySettings.smsNotifications}
                      onChange={(e) => handleSecurityChange('smsNotifications', e.target.checked)}
                      size="small"
                    />
                  }
                  label={
                    <Typography variant="body2">SMS Notifications</Typography>
                  }
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={securitySettings.loginAlerts}
                      onChange={(e) => handleSecurityChange('loginAlerts', e.target.checked)}
                      size="small"
                    />
                  }
                  label={
                    <Typography variant="body2">Login Alerts</Typography>
                  }
                />
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* Success Alert */}
      {!isEditing && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Profile updated successfully!
        </Alert>
      )}
    </Container>
  );
};

export default ProfilePage;
