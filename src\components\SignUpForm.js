import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Link,
  InputAdornment,
  IconButton,
  CircularProgress,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import { User, Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext.js';
import { toast } from 'sonner';
import GoogleLoginButton from './GoogleLoginButton.js';

const SignUpForm = ({ onSwitchToLogin }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});
  
  const { signUp, isLoading } = useAuth();

  const validateForm = () => {
    const newErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain uppercase, lowercase, and number';
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    // Terms validation
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = 'You must agree to the terms and conditions';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field) => (event) => {
    const value = field === 'agreeToTerms' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await signUp(formData.email, formData.password, formData.name);
      toast.success('Account created successfully! Please check your email for verification.');
    } catch (error) {
      console.error('Sign up error:', error);
      toast.error(error.message || 'Failed to create account. Please try again.');
    }
  };

  return React.createElement(
    Box,
    { sx: { width: '100%', maxWidth: 400 } },
    React.createElement(
      Box,
      { sx: { textAlign: 'center', mb: 4 } },
      React.createElement(
        Typography,
        { variant: "h4", sx: { fontWeight: 700, mb: 1, color: '#1a1a1a' } },
        "Create Account"
      ),
      React.createElement(
        Typography,
        { variant: "body1", sx: { color: '#666', mb: 3 } },
        "Join us today and get started"
      )
    ),
    React.createElement(GoogleLoginButton),
    React.createElement(
      Box,
      { sx: { display: 'flex', alignItems: 'center', my: 3 } },
      React.createElement(Box, { sx: { flex: 1, height: '1px', bgcolor: '#e0e0e0' } }),
      React.createElement(
        Typography,
        { variant: "body2", sx: { px: 2, color: '#666' } },
        "or"
      ),
      React.createElement(Box, { sx: { flex: 1, height: '1px', bgcolor: '#e0e0e0' } })
    ),
    React.createElement(
      "form",
      { onSubmit: handleSubmit },
      React.createElement(
        Box,
        { sx: { display: 'flex', flexDirection: 'column', gap: 2.5 } },
        React.createElement(TextField, {
          fullWidth: true,
          label: "Full Name",
          variant: "outlined",
          value: formData.name,
          onChange: handleInputChange('name'),
          error: Boolean(errors.name),
          helperText: errors.name,
          InputProps: {
            startAdornment: React.createElement(
              InputAdornment,
              { position: "start" },
              React.createElement(User, { size: 20, color: errors.name ? '#d32f2f' : '#666' })
            )
          },
          sx: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            }
          }
        }),
        React.createElement(TextField, {
          fullWidth: true,
          label: "Email Address",
          type: "email",
          variant: "outlined",
          value: formData.email,
          onChange: handleInputChange('email'),
          error: Boolean(errors.email),
          helperText: errors.email,
          InputProps: {
            startAdornment: React.createElement(
              InputAdornment,
              { position: "start" },
              React.createElement(Mail, { size: 20, color: errors.email ? '#d32f2f' : '#666' })
            )
          },
          sx: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            }
          }
        }),
        React.createElement(TextField, {
          fullWidth: true,
          label: "Password",
          type: showPassword ? "text" : "password",
          variant: "outlined",
          value: formData.password,
          onChange: handleInputChange('password'),
          error: Boolean(errors.password),
          helperText: errors.password,
          InputProps: {
            startAdornment: React.createElement(
              InputAdornment,
              { position: "start" },
              React.createElement(Lock, { size: 20, color: errors.password ? '#d32f2f' : '#666' })
            ),
            endAdornment: React.createElement(
              InputAdornment,
              { position: "end" },
              React.createElement(
                IconButton,
                {
                  onClick: () => setShowPassword(!showPassword),
                  edge: "end",
                  size: "small"
                },
                showPassword ? React.createElement(EyeOff, { size: 20 }) : React.createElement(Eye, { size: 20 })
              )
            )
          },
          sx: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            }
          }
        }),
        React.createElement(TextField, {
          fullWidth: true,
          label: "Confirm Password",
          type: showConfirmPassword ? "text" : "password",
          variant: "outlined",
          value: formData.confirmPassword,
          onChange: handleInputChange('confirmPassword'),
          error: Boolean(errors.confirmPassword),
          helperText: errors.confirmPassword,
          InputProps: {
            startAdornment: React.createElement(
              InputAdornment,
              { position: "start" },
              React.createElement(Lock, { size: 20, color: errors.confirmPassword ? '#d32f2f' : '#666' })
            ),
            endAdornment: React.createElement(
              InputAdornment,
              { position: "end" },
              React.createElement(
                IconButton,
                {
                  onClick: () => setShowConfirmPassword(!showConfirmPassword),
                  edge: "end",
                  size: "small"
                },
                showConfirmPassword ? React.createElement(EyeOff, { size: 20 }) : React.createElement(Eye, { size: 20 })
              )
            )
          },
          sx: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            }
          }
        }),
        React.createElement(
          FormControlLabel,
          {
            control: React.createElement(Checkbox, {
              checked: formData.agreeToTerms,
              onChange: handleInputChange('agreeToTerms'),
              color: "primary"
            }),
            label: React.createElement(
              Typography,
              { variant: "body2", sx: { color: errors.agreeToTerms ? '#d32f2f' : '#666' } },
              "I agree to the ",
              React.createElement(
                Link,
                { href: "#", sx: { color: '#1976d2', textDecoration: 'none' } },
                "Terms of Service"
              ),
              " and ",
              React.createElement(
                Link,
                { href: "#", sx: { color: '#1976d2', textDecoration: 'none' } },
                "Privacy Policy"
              )
            ),
            sx: { alignItems: 'flex-start', mt: 1 }
          }
        ),
        errors.agreeToTerms && React.createElement(
          Typography,
          { variant: "caption", sx: { color: '#d32f2f', mt: -1 } },
          errors.agreeToTerms
        ),
        React.createElement(
          Button,
          {
            type: "submit",
            fullWidth: true,
            variant: "contained",
            size: "large",
            disabled: isLoading,
            sx: {
              py: 1.5,
              borderRadius: 2,
              fontWeight: 600,
              textTransform: 'none',
              fontSize: '1rem',
              mt: 2
            }
          },
          isLoading ? React.createElement(CircularProgress, { size: 24, color: "inherit" }) : "Create Account"
        )
      )
    ),
    React.createElement(
      Box,
      { sx: { textAlign: 'center', mt: 3 } },
      React.createElement(
        Typography,
        { variant: "body2", sx: { color: '#666' } },
        "Already have an account? ",
        React.createElement(
          Link,
          {
            component: "button",
            type: "button",
            onClick: onSwitchToLogin,
            sx: {
              color: '#1976d2',
              textDecoration: 'none',
              fontWeight: 600,
              '&:hover': {
                textDecoration: 'underline'
              }
            }
          },
          "Sign in"
        )
      )
    )
  );
};

export default SignUpForm;
