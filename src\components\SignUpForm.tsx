
import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Link,
  InputAdornment,
  IconButton,
  CircularProgress,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import { User, Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'sonner';
import GoogleLoginButton from './GoogleLoginButton';

interface SignUpFormProps {
  onSwitchToLogin: () => void;
}

const SignUpForm: React.FC<SignUpFormProps> = ({ onSwitchToLogin }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  
  const { signUp, isLoading } = useAuth();

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/;
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    } else if (!passwordRegex.test(formData.password)) {
      newErrors.password = 'Password must contain uppercase, lowercase, and number';
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    // Terms validation
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = 'You must agree to the terms and conditions';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      await signUp(formData.name.trim(), formData.email, formData.password);
      toast.success('Account created successfully!');
    } catch (error) {
      toast.error('Failed to create account. Please try again.');
    }
  };

  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[^a-zA-Z0-9]/.test(password)) strength++;
    
    if (strength <= 2) return { text: 'Weak', color: '#f44336' };
    if (strength <= 3) return { text: 'Medium', color: '#ff9800' };
    return { text: 'Strong', color: '#4caf50' };
  };

  const passwordStrength = getPasswordStrength(formData.password);

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
      <Typography variant="h5" component="h1" gutterBottom sx={{ 
        fontWeight: 700, 
        color: '#1a1a1a',
        mb: 1,
        textAlign: 'center',
        fontSize: { xs: '1.3rem', sm: '1.5rem' }
      }}>
        Create Account
      </Typography>
      
      <Typography variant="body2" sx={{ 
        color: '#666', 
        mb: 3, 
        textAlign: 'center',
        fontSize: '0.875rem'
      }}>
        Join us today and get started
      </Typography>

      <GoogleLoginButton />
      
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        my: 2.5,
        '&::before, &::after': {
          content: '""',
          flex: 1,
          height: '1px',
          backgroundColor: '#e0e0e0',
        },
      }}>
        <Typography sx={{ px: 2, color: '#666', fontSize: '0.8rem' }}>
          or continue with email
        </Typography>
      </Box>

      <TextField
        fullWidth
        label="Full Name"
        type="text"
        value={formData.name}
        onChange={(e) => handleInputChange('name', e.target.value)}
        error={!!errors.name}
        helperText={errors.name}
        disabled={isLoading}
        size="small"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <User size={18} color="#666" />
            </InputAdornment>
          ),
        }}
        sx={{ 
          mb: 1.5,
          '& .MuiOutlinedInput-root': {
            borderRadius: 1.5,
          },
        }}
      />

      <TextField
        fullWidth
        label="Email Address"
        type="email"
        value={formData.email}
        onChange={(e) => handleInputChange('email', e.target.value)}
        error={!!errors.email}
        helperText={errors.email}
        disabled={isLoading}
        size="small"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Mail size={18} color="#666" />
            </InputAdornment>
          ),
        }}
        sx={{ 
          mb: 1.5,
          '& .MuiOutlinedInput-root': {
            borderRadius: 1.5,
          },
        }}
      />

      <TextField
        fullWidth
        label="Password"
        type={showPassword ? 'text' : 'password'}
        value={formData.password}
        onChange={(e) => handleInputChange('password', e.target.value)}
        error={!!errors.password}
        helperText={errors.password}
        disabled={isLoading}
        size="small"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Lock size={18} color="#666" />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <IconButton
                onClick={() => setShowPassword(!showPassword)}
                edge="end"
                size="small"
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </IconButton>
            </InputAdornment>
          ),
        }}
        sx={{ 
          mb: 0.5,
          '& .MuiOutlinedInput-root': {
            borderRadius: 1.5,
          },
        }}
      />

      {formData.password && (
        <Box sx={{ mb: 1.5 }}>
          <Typography variant="caption" sx={{ color: passwordStrength.color, fontWeight: 600, fontSize: '0.75rem' }}>
            Password strength: {passwordStrength.text}
          </Typography>
        </Box>
      )}

      <TextField
        fullWidth
        label="Confirm Password"
        type={showConfirmPassword ? 'text' : 'password'}
        value={formData.confirmPassword}
        onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
        error={!!errors.confirmPassword}
        helperText={errors.confirmPassword}
        disabled={isLoading}
        size="small"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Lock size={18} color="#666" />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <IconButton
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                edge="end"
                size="small"
              >
                {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </IconButton>
            </InputAdornment>
          ),
        }}
        sx={{ 
          mb: 1.5,
          '& .MuiOutlinedInput-root': {
            borderRadius: 1.5,
          },
        }}
      />

      <FormControlLabel
        control={
          <Checkbox
            checked={formData.agreeToTerms}
            onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
            disabled={isLoading}
            size="small"
          />
        }
        label={
          <Typography variant="body2" sx={{ color: '#666', fontSize: '0.8rem' }}>
            I agree to the{' '}
            <Link href="#" sx={{ color: '#1976d2', textDecoration: 'none' }}>
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="#" sx={{ color: '#1976d2', textDecoration: 'none' }}>
              Privacy Policy
            </Link>
          </Typography>
        }
        sx={{ mb: 1.5, alignItems: 'flex-start' }}
      />

      {errors.agreeToTerms && (
        <Typography variant="caption" sx={{ color: '#f44336', mb: 1.5, display: 'block', fontSize: '0.75rem' }}>
          {errors.agreeToTerms}
        </Typography>
      )}

      <Button
        fullWidth
        type="submit"
        variant="contained"
        disabled={isLoading}
        size="medium"
        sx={{
          py: 1.25,
          fontSize: '0.9rem',
          fontWeight: 600,
          textTransform: 'none',
          borderRadius: 1.5,
          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
          boxShadow: '0 3px 10px rgba(25, 118, 210, 0.3)',
          '&:hover': {
            background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
            boxShadow: '0 4px 12px rgba(25, 118, 210, 0.4)',
          },
          '&:disabled': {
            background: '#ccc',
            boxShadow: 'none',
          },
        }}
      >
        {isLoading ? (
          <CircularProgress size={20} color="inherit" />
        ) : (
          'Create Account'
        )}
      </Button>

      <Typography variant="body2" sx={{ mt: 2.5, textAlign: 'center', color: '#666', fontSize: '0.85rem' }}>
        Already have an account?{' '}
        <Link
          component="button"
          type="button"
          onClick={onSwitchToLogin}
          sx={{ 
            color: '#1976d2',
            textDecoration: 'none',
            fontWeight: 600,
            '&:hover': {
              textDecoration: 'underline',
            },
          }}
        >
          Sign in
        </Link>
      </Typography>
    </Box>
  );
};

export default SignUpForm;
