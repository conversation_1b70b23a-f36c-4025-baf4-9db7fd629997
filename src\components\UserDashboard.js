import React from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  Button,
  Avatar,
  Chip,
  Card,
  CardContent,
  AppBar,
  Toolbar,
  IconButton,
  Badge,
  Menu,
  MenuItem,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { 
  LogOut, 
  User, 
  Mail, 
  CheckCircle, 
  Settings, 
  Bell,
  Menu as MenuIcon,
  LayoutDashboard,
  BarChart3,
  Users,
  FileText,
  HelpCircle,
  Home,
  Activity,
  Calendar,
  MessageSquare
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext.js';

const UserDashboard = () => {
  const { user, logout } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [notificationsAnchor, setNotificationsAnchor] = React.useState(null);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationsOpen = (event) => {
    setNotificationsAnchor(event.currentTarget);
  };

  const handleNotificationsClose = () => {
    setNotificationsAnchor(null);
  };

  const handleLogout = async () => {
    await logout();
    handleProfileMenuClose();
  };

  const menuItems = [
    { text: 'Dashboard', icon: LayoutDashboard, active: true },
    { text: 'Analytics', icon: BarChart3, active: false },
    { text: 'Users', icon: Users, active: false },
    { text: 'Reports', icon: FileText, active: false },
    { text: 'Calendar', icon: Calendar, active: false },
    { text: 'Messages', icon: MessageSquare, active: false },
    { text: 'Activity', icon: Activity, active: false },
    { text: 'Settings', icon: Settings, active: false },
    { text: 'Help', icon: HelpCircle, active: false },
  ];

  const notifications = [
    { id: 1, message: 'New user registered', time: '2 minutes ago', unread: true },
    { id: 2, message: 'Report generated successfully', time: '1 hour ago', unread: true },
    { id: 3, message: 'System maintenance scheduled', time: '3 hours ago', unread: false },
    { id: 4, message: 'New feature released', time: '1 day ago', unread: false },
  ];

  const unreadCount = notifications.filter(n => n.unread).length;

  const stats = [
    { title: 'Total Users', value: '2,847', change: '+12%', color: '#4caf50' },
    { title: 'Revenue', value: '$45,210', change: '+8%', color: '#2196f3' },
    { title: 'Orders', value: '1,234', change: '+15%', color: '#ff9800' },
    { title: 'Conversion', value: '3.2%', change: '-2%', color: '#f44336' },
  ];

  const drawerContent = React.createElement(
    Box,
    { sx: { width: 280, height: '100%', bgcolor: '#1a1a1a', color: 'white' } },
    React.createElement(
      Box,
      { sx: { p: 3, borderBottom: '1px solid #333' } },
      React.createElement(
        Typography,
        { variant: "h6", sx: { fontWeight: 700, color: '#fff' } },
        "Dashboard"
      )
    ),
    React.createElement(
      List,
      { sx: { px: 2, py: 1 } },
      menuItems.map((item, index) => React.createElement(
        ListItem,
        {
          key: index,
          button: true,
          sx: {
            borderRadius: 2,
            mb: 0.5,
            bgcolor: item.active ? 'rgba(33, 150, 243, 0.1)' : 'transparent',
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.05)',
            },
          }
        },
        React.createElement(
          ListItemIcon,
          { sx: { color: item.active ? '#2196f3' : '#999', minWidth: 40 } },
          React.createElement(item.icon, { size: 20 })
        ),
        React.createElement(
          ListItemText,
          {
            primary: item.text,
            sx: {
              '& .MuiTypography-root': {
                fontSize: '0.9rem',
                fontWeight: item.active ? 600 : 400,
                color: item.active ? '#2196f3' : '#ccc',
              },
            }
          }
        )
      ))
    )
  );

  return React.createElement(
    Box,
    { sx: { display: 'flex', minHeight: '100vh' } },
    React.createElement(
      AppBar,
      {
        position: "fixed",
        sx: {
          zIndex: theme.zIndex.drawer + 1,
          bgcolor: '#fff',
          color: '#333',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
          borderBottom: '1px solid #e0e0e0',
        }
      },
      React.createElement(
        Toolbar,
        { sx: { justifyContent: 'space-between' } },
        React.createElement(
          Box,
          { sx: { display: 'flex', alignItems: 'center' } },
          isMobile && React.createElement(
            IconButton,
            {
              color: "inherit",
              edge: "start",
              onClick: handleDrawerToggle,
              sx: { mr: 2 }
            },
            React.createElement(MenuIcon)
          ),
          React.createElement(
            Typography,
            { variant: "h6", sx: { fontWeight: 700, color: '#1a1a1a' } },
            "Welcome back, ", user?.name || user?.email || 'User'
          )
        ),
        React.createElement(
          Box,
          { sx: { display: 'flex', alignItems: 'center', gap: 1 } },
          React.createElement(
            IconButton,
            { onClick: handleNotificationsOpen, color: "inherit" },
            React.createElement(
              Badge,
              { badgeContent: unreadCount, color: "error" },
              React.createElement(Bell, { size: 20 })
            )
          ),
          React.createElement(
            IconButton,
            { onClick: handleProfileMenuOpen, sx: { p: 0.5 } },
            React.createElement(
              Avatar,
              {
                sx: { width: 36, height: 36, bgcolor: '#2196f3' },
                src: user?.avatar
              },
              user?.name?.[0] || user?.email?.[0] || 'U'
            )
          )
        )
      )
    ),
    !isMobile && React.createElement(
      Drawer,
      {
        variant: "permanent",
        sx: {
          width: 280,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 280,
            boxSizing: 'border-box',
            bgcolor: '#1a1a1a',
            borderRight: 'none',
          },
        }
      },
      React.createElement(Toolbar),
      drawerContent
    ),
    isMobile && React.createElement(
      Drawer,
      {
        variant: "temporary",
        open: mobileOpen,
        onClose: handleDrawerToggle,
        ModalProps: { keepMounted: true },
        sx: {
          '& .MuiDrawer-paper': {
            width: 280,
            boxSizing: 'border-box',
            bgcolor: '#1a1a1a',
          },
        }
      },
      drawerContent
    ),
    React.createElement(
      Box,
      {
        component: "main",
        sx: {
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - 280px)` },
          bgcolor: '#f8f9fa',
          minHeight: '100vh',
        }
      },
      React.createElement(Toolbar),
      React.createElement(
        Container,
        { maxWidth: "xl" },
        React.createElement(
          Typography,
          { variant: "h4", sx: { mb: 3, fontWeight: 700, color: '#1a1a1a' } },
          "Dashboard Overview"
        ),
        React.createElement(
          Box,
          { sx: { display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 3, mb: 4 } },
          stats.map((stat, index) => React.createElement(
            Card,
            {
              key: index,
              sx: {
                borderRadius: 2,
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                border: '1px solid #e0e0e0',
              }
            },
            React.createElement(
              CardContent,
              null,
              React.createElement(
                Typography,
                { variant: "body2", color: "textSecondary", sx: { mb: 1 } },
                stat.title
              ),
              React.createElement(
                Typography,
                { variant: "h4", sx: { fontWeight: 700, mb: 1 } },
                stat.value
              ),
              React.createElement(
                Chip,
                {
                  label: stat.change,
                  size: "small",
                  sx: {
                    bgcolor: stat.color,
                    color: 'white',
                    fontWeight: 600,
                  }
                }
              )
            )
          ))
        )
      )
    ),
    React.createElement(
      Menu,
      {
        anchorEl: anchorEl,
        open: Boolean(anchorEl),
        onClose: handleProfileMenuClose,
        transformOrigin: { horizontal: 'right', vertical: 'top' },
        anchorOrigin: { horizontal: 'right', vertical: 'bottom' },
      },
      React.createElement(
        MenuItem,
        { onClick: handleProfileMenuClose },
        React.createElement(User, { size: 16, style: { marginRight: 8 } }),
        "Profile"
      ),
      React.createElement(
        MenuItem,
        { onClick: handleProfileMenuClose },
        React.createElement(Settings, { size: 16, style: { marginRight: 8 } }),
        "Settings"
      ),
      React.createElement(Divider),
      React.createElement(
        MenuItem,
        { onClick: handleLogout },
        React.createElement(LogOut, { size: 16, style: { marginRight: 8 } }),
        "Logout"
      )
    ),
    React.createElement(
      Menu,
      {
        anchorEl: notificationsAnchor,
        open: Boolean(notificationsAnchor),
        onClose: handleNotificationsClose,
        transformOrigin: { horizontal: 'right', vertical: 'top' },
        anchorOrigin: { horizontal: 'right', vertical: 'bottom' },
        PaperProps: { sx: { width: 320, maxHeight: 400 } },
      },
      React.createElement(
        Box,
        { sx: { p: 2, borderBottom: '1px solid #e0e0e0' } },
        React.createElement(
          Typography,
          { variant: "h6", sx: { fontWeight: 600 } },
          "Notifications"
        )
      ),
      notifications.map((notification) => React.createElement(
        MenuItem,
        {
          key: notification.id,
          onClick: handleNotificationsClose,
          sx: {
            py: 1.5,
            borderLeft: notification.unread ? '3px solid #2196f3' : '3px solid transparent'
          }
        },
        React.createElement(
          Box,
          null,
          React.createElement(
            Typography,
            {
              variant: "body2",
              sx: {
                fontWeight: notification.unread ? 600 : 400,
                mb: 0.5
              }
            },
            notification.message
          ),
          React.createElement(
            Typography,
            { variant: "caption", color: "textSecondary" },
            notification.time
          )
        )
      )),
      React.createElement(Divider),
      React.createElement(
        MenuItem,
        { onClick: handleNotificationsClose, sx: { justifyContent: 'center' } },
        React.createElement(
          Typography,
          { variant: "body2", color: "primary" },
          "View All Notifications"
        )
      )
    )
  );
};

export default UserDashboard;
