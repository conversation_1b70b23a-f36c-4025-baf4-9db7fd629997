import React from "react"
import { Toaster as Son<PERSON>, toast } from "sonner"

const Toaster = ({ ...props }) => {
  return React.createElement(Son<PERSON>, {
    theme: "system",
    className: "toaster group",
    toastOptions: {
      classNames: {
        toast:
          "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
        description: "group-[.toast]:text-muted-foreground",
        actionButton:
          "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
        cancelButton:
          "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
      },
    },
    ...props
  })
}

export { Toaster, toast }
