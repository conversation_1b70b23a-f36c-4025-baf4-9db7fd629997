
import React, { createContext, useContext, useState } from 'react';

const AuthContext = createContext(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const login = async (email, password) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock successful login
      const mockUser = {
        id: '1',
        email: email,
        name: email.split('@')[0],
      };
      
      setUser(mockUser);
      console.log('Login successful:', { email, password: '***' });
    } catch (error) {
      console.error('Login failed:', error);
      throw new Error('Invalid credentials');
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (name, email, password) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock successful registration
      const mockUser = {
        id: '1',
        email: email,
        name: name,
      };
      
      setUser(mockUser);
      console.log('Sign up successful:', { name, email, password: '***' });
    } catch (error) {
      console.error('Sign up failed:', error);
      throw new Error('Registration failed');
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithGoogle = async () => {
    setIsLoading(true);
    try {
      // Simulate Google OAuth flow
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock successful Google login
      const mockUser = {
        id: 'google_1',
        email: '<EMAIL>',
        name: 'Google User',
        profilePicture: 'https://via.placeholder.com/40',
      };
      
      setUser(mockUser);
      console.log('Google login successful');
    } catch (error) {
      console.error('Google login failed:', error);
      throw new Error('Google authentication failed');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = (): void => {
    setUser(null);
    console.log('User logged out');
  };

  const forgotPassword = async (email) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      console.log('Password reset email sent to:', email);
    } catch (error) {
      console.error('Forgot password failed:', error);
      throw new Error('Failed to send reset email');
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (token, newPassword) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      console.log('Password reset successful');
    } catch (error) {
      console.error('Password reset failed:', error);
      throw new Error('Failed to reset password');
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    isLoading,
    login,
    signUp,
    loginWithGoogle,
    logout,
    forgotPassword,
    resetPassword,
  };

  return React.createElement(AuthContext.Provider, { value: value }, children);
};
