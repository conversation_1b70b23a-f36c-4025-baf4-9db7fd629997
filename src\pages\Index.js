
import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider } from '../contexts/AuthContext';
import AuthContainer from '../components/AuthContainer.js';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
  },
  shape: {
    borderRadius: 8,
  },
});

const Index = () => {
  return React.createElement(
    ThemeProvider,
    { theme: theme },
    React.createElement(CssBaseline),
    React.createElement(
      AuthProvider,
      null,
      React.createElement(AuthContainer)
    )
  );
};

export default Index;
